import React, { forwardRef } from 'react';
import Select, { components, MultiValue, MultiValueRemoveProps } from 'react-select';
import { cn } from '@/lib/utils';
import '@/styles/multiselectStyle.css';
import { X } from 'lucide-react';

type Option = { id: string; value: string; label: string };

interface CustomSelectProps {
  options: Option[];
  value: Option | Option[] | null;
  onChange: (value: Option | Option[] | null) => void;
  placeholder?: string;
  disabled?: boolean;
  className?: string;
  onMenuOpen?: () => void;
  onMenuClose?: () => void;
  isMulti?: boolean;
}

const CustomMultiValueRemove = (props: MultiValueRemoveProps<Option>) => (
  <components.MultiValueRemove {...props}>
    <div
      onClick={e => {
        e.stopPropagation();
        props.innerProps.onClick?.(e);
      }}
    >
      <X className="h-3 w-3 cursor-pointer" />
    </div>
  </components.MultiValueRemove>
);

export const CustomSelect = forwardRef<any, CustomSelectProps>(
  (
    {
      options,
      value,
      onChange,
      placeholder,
      disabled,
      className,
      onMenuOpen,
      onMenuClose,
      isMulti = false,
    },
    ref
  ) => {
    const handleChange = (selectedOptions: MultiValue<Option> | Option | null) => {
      if (isMulti) {
        onChange(selectedOptions as Option[]);
      } else {
        onChange(selectedOptions as Option | null);
      }
    };

    return (
      <Select
        ref={ref}
        isMulti={isMulti}
        options={options}
        value={value}
        onChange={handleChange}
        placeholder={placeholder}
        isDisabled={disabled}
        className={cn('w-full text-base', className)}
        classNamePrefix="custom-select"
        components={{
          ...(isMulti && { MultiValueRemove: CustomMultiValueRemove }),
          IndicatorSeparator: () => null,
        }}
        onMenuOpen={onMenuOpen}
        onMenuClose={onMenuClose}
      />
    );
  }
);
