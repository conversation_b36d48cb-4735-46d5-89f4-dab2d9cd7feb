{"common": {"search": "Search", "filter": "Filter", "create": "CREATE", "save": "SAVE", "submit": "Submit", "cancel": "CANCEL", "delete": "Delete", "add": "ADD", "clone": "<PERSON><PERSON>", "export": "Export", "edit": "Edit", "yes": "YES", "no": "NO", "selectOption": "Select option", "getStarted": "GET STARTED", "preview": "Preview", "publish": "PUBLISH", "duplicate": "Duplicate", "versionHistory": "Version History", "flows": "Flows", "debugger": "Debugger", "message": "Message", "image": "Image", "file": "File", "video": "Video", "addViaUrl": "Add via URL", "enterFileUrl": "Enter file URL", "maxSize": "Max size: {{size}}MB", "clickOrDrag": "Click or drag {{type}} file here", "clickOrDragFiles": "Click or drag file to this area to upload", "writeMessage": "Write message", "typeMessage": "Type your message...", "fillAboveField": "Fill the form above to continue", "dateRange": "Pick a date range", "trackOrder": "Track my order", "cancelOrder": "Cancel my order", "chatWithAgent": "Chat with an Agent", "viewSimilarProducts": "View similar products", "hello": "Hello there, {{name}}!", "howCanIHelp": "How may I help you today?", "searchFlows": "Search flows...", "onboarding": "Onboarding", "notFound": "NotFound", "enterValidValue": "Please enter valid value", "translateTo": "Translate to", "translate": "TRANSLATE", "nothingToShow": "Nothing to show", "generate": "Generate", "close": "Close", "nodeId": "Node ID:", "noData": "No Data", "searchEllipsis": "Search...", "justNow": "just now", "update": "Update", "error": "Error", "somethingWrong": "Something went wrong", "saveChanges": "SAVE CHANGES", "saving": "Saving...", "date": "Date", "loading": "Loading...", "build": "Build", "import": "Import", "share": "Share", "goBack": "Go Back", "noNode": "No Node found", "versions": "Versions", "intent": "Intent"}, "chatbot": {"botPublishedSuccessfully": "Chatbot published successfully!", "untitled": "Untitled", "noDomain": "DEFAULT", "noDescription": "No description", "confirmDelete": "CONFIRM DELETE", "deleteMessage": "Are you sure you want to delete this chatbot?", "chatbotDeleted": "<PERSON><PERSON><PERSON> deleted successfully", "noCancel": "NO, CANCEL", "yesDelete": "YES, DELETE", "newChatbotPrefix": "<PERSON>_<PERSON><PERSON><PERSON>", "cloneFailed": "Failed to clone chatbot.", "buildSuccess": "Bot built successfully!", "buildFailed": "Bot build failed.", "buildStreamError": "Error connecting to build stream.", "defaultTitle": "My Chatbot", "defaultDomain": "Ecom", "defaultDescription": "Help customers navigate the digital purchasing process."}, "debugger": {"logs": "Logs", "aiAnalysis": "AI Analysis", "sessionData": "Session Data", "aiAnalysisContent": "AI Analysis content coming soon.", "sessionDataContent": "Session Data content coming soon.", "noAiAnalysisLogs": "No AI Analysis logs available.", "noSessionDataLogs": "No Session Data logs available.", "noLogs": "No logs available."}, "preview": {"confirmDialog": "Want to end this conversation?", "confirmDialogDesc": "This will erase the chat and close the window"}, "validation": {"maxLength": "This field cannot exceed {{count}} characters.", "BLANK_URL": "URL cannot be empty.", "URL_TOO_LONG": "URL is too long.", "INVALID_URL": "Invalid URL format.", "URL_DISPLAY_TEXT_TOO_LONG": "Display text cannot exceed 100 characters.", "invalidPhoneNumber": "Invalid phone number for {{field}}.", "fieldRequired": "{{field}} is required.", "invalidEmail": "Invalid email for {{field}}.", "passwordMinLength": "Password for {{field}} must be at least 6 characters.", "invalidTimeFormat": "Invalid time format for {{field}}.", "invalidDate": "Invalid date for {{field}}.", "pastDateRequired": "{{field}} must be a past date (before {{date}})", "futureDateRequired": "{{field}} must be a future date (after {{date}})", "invalidNumber": "Invalid number for {{field}}"}, "home": {"title": "NeuraTalk AI", "description": "is a cutting-edge conversational AI solution designed to enhance customer engagement, automate support, and streamline business operations.", "noResults": "0 results found", "lastUpdated": "Last updated {{date}}"}, "editor": {"chatbotName": "Chatbot Name", "domain": "Domain", "description": "Description", "uploadImage": "Click or drag file to this area to upload", "uploadFormat": "(Size: Up to 5MB | Format: jpg, png)", "unsupportedFile": "Unsupported file type", "fileTooLarge": "File must be less than 2MB", "invalidName": "Only letters, numbers, hyphens (-), underscores (), and periods (.) are allowed", "invalidImageFile": "Please drop a valid image file (png, jpg, jpeg, webp, gif, svg)", "nameRequired": "<PERSON><PERSON><PERSON> name is required", "nameMaxError": "Chatbot name cannot exceed 50 characters", "domainRequired": "Domain is required", "descMaxError": "Description cannot exceed 150 characters", "updateSuccess": "Chatbot updated successfully", "descRequired": "Description is required", "updateError": "Failed to update bot", "writeMessage": "Write Message"}, "navigation": {"neuraTalk": "NeuraTalk", "create": "Create"}, "domains": {"ecomm": "Ecomm", "telecom": "Telecom", "retail": "Retail", "travel": "Travel", "other": "Other"}, "emptyState": {"title": "Nothing here yet", "description": "There is currently no content to display."}, "intents": {"title": "Intents", "addTitle": "ADD INTENT", "editTitle": "EDIT INTENT", "name": "Intent Name", "namePlaceholder": "Intent name", "nameLabel": "Intent name", "nameRequired": "Intent name is required.", "startAdding": "Start adding intents", "noFlowsConnected": "No flows connected", "selectToManage": "Select an intent to manage utterances", "loading": "Loading intents.", "loadingError": "Error loading intents.", "intentAdded": "Intent added successfully.", "intentUpdated": "Intent updated successfully.", "intentDeleted": "Intent deleted successfully.", "confirmDeleteTitle": "CONFIRM INTENT DELETION", "deleteConfirmationMessage": "Are you sure you want to delete this intent?", "utterances": {"title": "Utterances", "addTitle": "ADD UTTERANCE", "editTitle": "EDIT UTTERANCE", "enterPlaceholder": "Enter utterance", "startAdding": "Start adding utterances", "emptyError": "Utterance cannot be empty.", "loading": "Loading utterances.", "loadingError": "Error loading utterances.", "utteranceAdded": "<PERSON><PERSON><PERSON> added.", "utteranceUpdated": "Utterance updated.", "utteranceDeleted": "Utterance deleted.", "confirmDeleteTitle": "CONFIRM UTTERANCE DELETION", "deleteConfirmationMessage": "Are you sure you want to delete this utterance?"}}, "entities": {"title": "Entities", "addTitle": "ADD ENTITY", "entityName": "Entity Name", "entityNamePlaceholder": "Entity name", "type": "Type", "selectType": "Type", "enablePartialMatch": "Enable partial match", "startAdding": "Start adding entities", "loading": "Loading entities...", "error": "Error loading entities.", "noEntitiesFound": "No entities found", "searchEntities": "Search entities...", "selected": "Selected", "removeEntity": "Remove entity", "types": {"text": "Text", "list": "List", "regex": "REGEX"}, "table": {"name": "Name", "type": "Type", "value": "Value", "action": "Action"}, "validation": {"nameRequired": "Entity name is required.", "typeRequired": "Entity type is required.", "valueRequired": "Value is required."}, "addValue": "Add value", "editTitle": "EDIT ENTITY", "regexValuePlaceholder": "Regex value", "entityAdded": "Entity added successfully.", "entityUpdated": "Entity updated successfully.", "entityDeleted": "Entity deleted successfully.", "confirmDeleteTitle": "CONFIRM ENTITY DELETION", "deleteConfirmationMessage": "Are you sure you want to delete this entity?"}, "train": {"entities": {"title": "Entities", "content": "Entities Content", "addTitle": "ADD ENTITY", "nameLabel": "Entity name", "intentIdLabel": "Intent ID", "metadataLabel": "<PERSON><PERSON><PERSON> (JSON)", "metadataPlaceholder": "Enter metadata as JSON", "loading": "Loading entities...", "error": "Error loading entities.", "validation": {"nameRequired": "Entity name is required.", "intentIdRequired": "Intent ID is required.", "invalidJson": "Invalid JSON format for metadata."}}, "synonyms": {"title": "Synonyms", "content": "Synonyms Content"}, "smallTalk": {"title": "Small Talk", "content": "Small Talk Content"}, "trainFromLogs": {"title": "Train from Logs", "content": "Train from Logs Content"}, "tabs": {"intentUtterances": "Intent Utterances", "entities": "Entities", "faqs": "FAQs", "synonyms": "Synonyms", "smallTalk": "Small Talk", "trainFromLogs": "Train from Logs"}}, "faqs": {"title": "Questions & Answers", "category": {"title": "Category", "addTitle": "ADD CATEGORY", "editTitle": "EDIT CATEGORY", "nameLabel": "Category name", "nameRequired": "Category name is required.", "startAdding": "Start adding categories", "selectToManage": "Select a category to manage questions", "categoryAdded": "Category added successfully.", "categoryUpdated": "Category updated successfully.", "categoryDeleted": "Category deleted successfully.", "confirmDeleteTitle": "CONFIRM CATEGORY DELETION", "deleteConfirmationMessage": "Are you sure you want to delete this category?"}, "loading": "Loading FAQs.", "loadingError": "Error loading FAQs.", "items": {"loading": "Loading FAQ items...", "loadingError": "Error loading FAQ items.", "startAdding": "Start adding questions", "addTitle": "ADD QUESTION", "editTitle": "EDIT QUESTION", "questionLabel": "Questions", "questionPlaceholder": "Enter question", "questionEmpty": "Question cannot be empty.", "atLeastOne": "At least one question is required.", "answerLabel": "Answer", "answerPlaceholder": "Enter answer", "answerEmpty": "Answer cannot be empty.", "linkFlowLabel": "Link Flow", "chooseFlowPlaceholder": "Choose Flow", "primaryLabel": "Primary", "questionPrefix": "Q", "answerPrefix": "A", "questionsAdded": "Questions added.", "questionsUpdated": "Questions updated.", "maxQuestions": "You can add a maximum of {{count}} questions.", "questionsDeleted": "Questions deleted.", "confirmDeleteTitle": "CONFIRM FAQ DELETION", "deleteConfirmationMessage": "Are you sure you want to delete this FAQ item?"}, "validation": {"questionRequired": "Question is required.", "atLeastOneQuestion": "At least one question is required.", "answerRequired": "Answer is required.", "isRequired": "is required", "validDate": "is required and must be a valid date", "pastDate": "must be a past date (before", "futureDate": "must be a future date (after"}}, "fileUpload": {"fileTooLarge": "File must be under {{size}}MB and of type {{type}}", "someFilesRejected": "Some files were rejected. Ensure correct types and size < {{size}}MB.", "failedToUpload": "Failed to upload: {{filename}}"}, "tabs": {"contentComingSoon": "Content for {{tabName}} tab coming soon"}, "builder": {"tabs": {"design": "Design", "train": "Train", "channels": "Channels", "agentTransfer": "Agent Transfer", "integrations": "Integrations", "settings": "Settings"}}, "flows": {"untitledFlow": "untitledflow", "noFlows": "No Flows", "welcome": "Welcome", "fallback": "Fallback", "targetFlow": "Target Flow", "existingFlow": "Remember context between the connection flow", "errorLoading": "Error loading flows", "newFlow": "New Flow", "fetchError": "Failed to fetch flows", "flowDelete": "Flow deleted successfully", "flowCreated": "New Flow Added Successfully", "flowNotCreated": "Couldn’t create flow", "flowDeleted": "Flow deleted successfully", "flowNotDeleted": "Couldn’t delete flow", "flowDuplicated": "Flow duplicated successfully", "flowNotDuplicated": "Couldn’t duplicate the flow", "flowRenamed": "Flow renamed successfully", "flowNotRenamed": "Couldn’t rename the flow"}, "agentTransfer": {"transfer": "Integrate Agent from the ‘Transfer Agent’ page to configure the Native Agent", "selectAgentTransfer": "Select an Agent Transfer", "nothingSelected": "Nothing is selected", "connectingToAgent": "Connecting to agent...", "waitingForAgentMessage": "Please wait while we connect you to an agent", "filters": {"all": "All", "native": "Native", "thirdParty": "Third Party"}, "tabs": {"available": "Available", "myAgentTransfers": "My Agent Transfers"}, "setupHeading": "Set up", "setupDescription": "Provide the details below to activate {{agentTransferName}} support for the chatbot.", "generateToken": "GENERATE TOKEN", "liveAgentPortalDetails": "Live Agent Portal Details", "pendingStatus": "Pending", "remove": "REMOVE", "liveStatus": "Live", "chatbotNameLabel": "Chatbot name:", "accessTokenLabel": "Access Token:", "shareInstruction": "Share the above with the Agent Admin for activation."}, "settings": {"language": "Language", "nlu": "NLU", "personalization": "Personalization", "llmConfiguration": "LLM Configuration", "cannedResponses": "Canned Responses", "loremDescription": "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor", "languages": "Languages", "yourLanguages": "Your Languages", "noLanguagesSelected": "No languages selected", "availableLanguages": "Available Languages", "searchLanguages": "Search languages...", "defaultTag": "<PERSON><PERSON><PERSON>", "allSelectedLanguages": "All selected languages", "languagesSaved": "Languages saved successfully"}, "stencil": {"nodes": "Nodes", "searchNodes": "Search Nodes...", "engage": "Engage", "utilities": "Utilities", "marketplace": "Marketplace"}, "platform": {"web": "Web", "mobile": "Mobile"}, "pagination": {"previous": "Previous", "next": "Next", "morePages": "More pages", "loadingMore": "Loading more...", "noItemsFound": "No items found", "errorLoadingData": "Error loading data", "tryAgain": "Try Again", "defaultFilterUI": "Default filter UI - customize with children prop"}, "form": {"loadingForm": "Loading form...", "typing": "Typing...", "enterLabel": "Enter label", "prompt": "Prompt", "textField": "Text Field", "label": "Label", "lableRequired": "Label is required", "promptRequired": "Prompt is required"}, "errors": {"failedToSend": "Failed to send message", "unexpectedResponse": "Unexpected response from server", "somethingWrong": "Something went wrong"}, "channels": {"selectWABA": "Select a WABA Number to connect", "changeNumber": "CHANGE NUMBER", "webhook": "Webhook", "webhookInstruction": "This webhook is now linked with your WABA number account and ready to receive messages. Copy this webhook for your reference.", "switchToMeta": "Switch to Meta Cloud API", "switchDescription": "Switch to Meta Cloud API and link your Chatbot via the partner BSP.", "switch": "SWITCH", "connect": "CONNECT", "selectChannels": "Select channels to configure", "nothingSelected": "Nothing is selected", "myChannels": "My Channels", "whatsapp": "WhatsApp", "telegram": "Telegram", "voice": "Voice", "alexa": "Alexa", "available": "Available", "invalid": "INVALID", "testChannel": "Test Channel", "getStarted": "GET STARTED", "metaCloudAPI": "Meta Cloud API", "ngage": "NGAGE", "sms": "SMS", "virtualReceptionist": "Virtual Receptionist", "email": "Email", "rcs": "RCS", "chatbot": "<PERSON><PERSON><PERSON>", "network": "Network", "studio": "Studio", "allChannels": "All Channels", "filters": {"all": "All", "native": "Native", "text": "Text", "voice": "Voice"}, "tabs": {"available": "Available", "myChannels": "My Channels"}, "chatWidget": "Chat widget", "widgetSettings": "Widget <PERSON>s", "deploy": "Deploy", "botAvatar": "<PERSON><PERSON> avatar", "uploadAvatarDescription": "Upload an avatar that reflects your business.", "uploadImage": "Upload Image", "botDisplayName": "Bot display name", "botDescription": "Bot description", "fontsAndColorTheme": "Fonts & Color Theme", "setColorThemeDescription": "Set the color theme for your widget.", "primaryColor": "Primary color", "secondaryColor": "Secondary color", "tertiaryColor": "Tertiary color", "deployChatbot": "De<PERSON><PERSON>", "deployInstructions": "Deployment instructions will go here.", "enterYourMessage": "Enter your Message", "launchTheBot": "Launch the bot", "copyCodeInstructions": "Copy the code and paste it in the <body> of your website.", "experienceOnWebsite": "Experience on a website", "copyLinkToShareBot": "Copy link to Share bot", "addPrimaryColor": "Add a Primary color", "addSecondaryColor": "Add a Secondary color", "addTertiaryColor": "Add a Tertiary color", "fontSansSerif": "Sans-serif", "fontSerif": "<PERSON><PERSON>", "fontMonospace": "Monospace", "fontSizeSmall": "Small (14px)", "fontSizeMedium": "Medium (16px)", "fontSizeLarge": "Large (18px)", "selectFontWidget": "Select the font for your widget", "defaultBotDisplayName": "Bot", "defaultBotDescription": "How Can I Help You Today?", "web": "Web", "mobileSDK": "Mobile SDK", "metaLogo": "M", "bsp360Logo": "360", "bspLogo": "BSP"}, "nodes": {"agentTransfer": "Agent Transfer", "appEnd": "App End", "appStart": "App Start", "choice": "Choice", "choiceOption": "Choice Option", "feedback": "<PERSON><PERSON><PERSON>", "flowConnector": "Flow Connector", "http": "HTTP", "interactiveMessage": "Interactive Message", "language": "Language", "message": "Message", "notification": "Notification", "payment": "Payment", "script": "<PERSON><PERSON><PERSON>", "text": "Text", "waitDelay": "Wait Delay", "whatsapp": "WhatsApp"}, "bots": {"testBot": "Test Bot", "testChatbot": "Test Chatbot", "aChatbot": "A test chatbot", "aChatbotDescription": "A test chatbot description", "myFlow": "My Flow", "lastUpdatedToday": "Last updated today"}, "whatsapp": {"onboarding": {"ngage": {"description": "Onboard WABA using NGAGE WhatsApp channel and integrate it with your Chatbot."}, "meta": {"description": "Onboard WABA using Meta Cloud API and link your Chatbot via the partner BSP."}}}, "loading": {"hangTight": "Hang tight! We are setting up your workspace...", "almostThere": "We are almost there..."}, "timePicker": {"hour": "Hour", "min": "Min", "amPm": "AM/PM"}, "richTextEditor": {"bold": "Bold", "italic": "Italic", "underline": "Underline", "strikeThrough": "Strike Through", "highlight": "Highlight", "superscriptSubscript": "Superscript/Subscript", "emoji": "<PERSON><PERSON><PERSON>"}, "http": {"requestType": "Request Type", "url": "URL", "timeout": "Timeout", "headers": "Headers", "requestBody": "Request Body"}, "notification": {"notificationChannel": "Select Notification Channel", "configureMessage": "Configure Message", "configureSMS": "Configure SMS", "selectSenderID": "Select Sender ID", "recipientMSISDN": "Enter Recipient MSISDN", "configureEmail": "Configure <PERSON><PERSON>", "selectEmail": "Select Email Address", "recipientEmail": "Enter Recipient Email Address", "enterSubjectOfEmail": "Enter Subject of the Email"}, "versionControl": {"title": "Version Control", "description": "Manage and view different versions of your flow.", "previewPlaceholder": "Flow preview will be displayed here.", "historyTitle": "Version History ({{count}} Versions)", "noVersions": "No versions found for this flow.", "version": "Version {{version}}", "loadVersion": "Load Version", "revertToVersion": "Revert to this Version"}}