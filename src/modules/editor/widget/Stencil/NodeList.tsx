import React from 'react';
import NodeItem from './NodeItem';
import useNodeConfig from '@/modules/editor/hooks/useStencilNodeConfig';
import { useTranslation } from 'react-i18next';

interface NodeListProps {
  collapseStatus: boolean;
  currentTab: string;
  search: string;
  startDrag: (event: React.MouseEvent, type: string) => void;
}

const NodeList: React.FC<NodeListProps> = ({ collapseStatus, currentTab, search, startDrag }) => {
  const { enabledNodes, getNodes, getComingSoonNodes } = useNodeConfig();
  const { t } = useTranslation();
  const nodes = getNodes(currentTab, search);
  const comingSoonNodes = getComingSoonNodes(currentTab, search);

  const hasResults = nodes.length > 0 || comingSoonNodes.length > 0;

  return (
    <div className="h-auto max-h-[45vh] overflow-y-auto overflow-x-hidden px-5">
      <div className="grid grid-cols-2 md:grid-cols-3 gap-8 py-5 justify-items-center content-center">
        {collapseStatus ? (
          Object.keys(enabledNodes).map(category =>
            enabledNodes[category].map(({ type }) => (
              <NodeItem key={type} type={type} startDrag={startDrag} />
            ))
          )
        ) : hasResults ? (
          <>
            {nodes.map(({ type }) => (
              <NodeItem key={type} type={type} startDrag={startDrag} />
            ))}
            {comingSoonNodes.map(({ type }) => (
              <NodeItem key={type} type={type} startDrag={startDrag} isComingSoon />
            ))}
          </>
        ) : search && (
          <div className="col-span-full text-center text-muted-foreground">
            {t('common.noNode')}
          </div>
        )}
      </div>
    </div>
  );
};

export default NodeList;
