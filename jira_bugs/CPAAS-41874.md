# CPAAS-41874: The language dropdown is not responsive as per the screen ratio. Also, provide a scroller on the language dropdown.

**Status:** To Do  
**Priority:** Medium

---

## Description

ISSUE SUMMARY: The language dropdown is not responsive as per the screen ratio. Also, provide a scroller on the language dropdown.Steps To Reproduce:-Log in to the Neuratalk portal  with valid EA/EU credentialsUser is redirected successfully to the Neuratalk homepage.Now, click on the Neuratalk AI module.Now, click on the edit icon on any bot present on the page or click on the create button.Now, click on the Settings tab.Click on the language card.Add multiple languages.Click on the Save button.Now navigate to the FAQs or Utterances list.Click on the language dropdown.Increase the screen ratio.Now again click on the language dropdown.Expected Results:-The dropdown should be responsive as per the screen ratio, and if the list is too big, please provide a scroller to scroll.Actual Results:-If the user increases the screen ratio, the dropdown values are going outside the page.Also, scroller is not visible on the dropdown list.ADDITIONAL DETAILS:
Browser & Version - Google Chrome Version 106.0.5249.103
Any Error/Warning in Console/Network tab/Logs -   NA
Other Environments where this issue exists -  NA
Any similar issue reported in the past -   NA
Any other behaviour to be taken into consideration by Dev before fixing this bug -  NATEST PROOFS:

---

## Comments

_No comments_