# CPAAS-41882: If the user adds utterances and FAQs in translated language, the language badges are not visible on the utterances and FAQs.

**Status:** To Do  
**Priority:** Medium

---

## Description

ISSUE SUMMARY: If the user adds utterances and FAQs in a translated language, the language badges are not visible on the utterances and FAQs.Steps To Reproduce:-Log in to the Neuratalk portal  with valid EA/EU credentialsUser is redirected successfully to the Neuratalk homepage.Now, click on the Neuratalk AI module.Now, click on the edit icon on any bot present on the page or click on the create button.Now, click on the Train tab or the Intent tab.Check the default language in the dropdown.Now add some questions or utterances.Now again click on the language dropdown and select any desired language.Now add the question/answers or utterances in the translated language.Click on the add button.Check the language badge on the question or utterances.Expected Results:-If the user has translated any question to the desired language, the language badge should be visible on the question.Actual Results:-No language badge is visible if the user translates any question and utterances into multiple languages.ADDITIONAL DETAILS:
Browser & Version - Google Chrome Version 106.0.5249.103
Any Error/Warning in Console/Network tab/Logs -   NA
Other Environments where this issue exists -  NA
Any similar issue reported in the past -   NA
Any other behaviour to be taken into consideration by Dev before fixing this bug -  NATEST PROOFS:

---

## Comments

_No comments_