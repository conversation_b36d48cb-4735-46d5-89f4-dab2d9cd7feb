# CPAAS-42048: Trigger Node| The page becomes unresponsive, and the user is unable to click any of the tabs after selecting the intent for the trigger node and then reopening the trigger node.

**Status:** To Do  
**Priority:** High

---

## Description

Description: Trigger Node| The page becomes unresponsive, and the user is unable to click any of the tabs after selecting the intent for the trigger node and then reopening the trigger node.Pre-requisite:
Intent implementation is doneSteps to reproduce:Log in to CPaaS  with Enterprise admin credentialsNow, navigate to NeuraTalk AI Click on the Create buttonOn the chatbot builder, click the Train tabSelect INTENT UTTERANCES sub-tabClick on the ADD INTENT buttonFill the details and ADD the intent Add some utterances to the intentIn the design tab, add a Trigger nodeClick on the Trigger node and link it with the created intentNow close the window.Now, reload the page.Click on the Trigger node to reopen the left window.Check the added Intent in the Label dropdown.Now click outside the window.Now, try to click on any of the tabs or nodes in the canvas.Expected Result:If the user closes the modal, all the buttons should function as expected.Actual Result:The user is unable to click on any of the buttons after the performed action.Screenshots:-

---

## Comments

_No comments_