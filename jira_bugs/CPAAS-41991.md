# CPAAS-41991: Multiple UI issues on the language page

**Status:** To Do  
**Priority:** Medium

---

## Description

ISSUE SUMMARY: Go back CTA button should be in caps.The border of the CTA buttons should not cross beyond the left pane; the left pane should be separate.The translated language below each of the languages should be the same as the upper one(14px)The font style of the CTA buttons should be Poppins Regular.The "Available languages" text should be 14 px and the color should be grey."Your language" text should be 14 px. Also, it should be in grey.There should be a text below the "Available Language" text indicating user to "choose the language from the list".The color of the "Go Back" text should be grey.The "Language" Title should be 16 px."Go Back" and "Save" buttons should be at the bottom right of the page.The alphabet separator should be 14pxSteps To Reproduce:-Log in to the Neuratalk portal  with valid EA/EU credentialsUser is redirected successfully to the Neuratalk homepage.Now, click on the Neuratalk AI module.Now, click on the edit icon on any bot present on the page or click on the create button.Now, click on the Settings tab.Select the language card on the settings page.Select any language.Check the design of the page.Expected Results:-The design should match XD.Actual Results:-The design does not match XD.ADDITIONAL DETAILS:
Browser & Version - Google Chrome Version 106.0.5249.103
Any Error/Warning in Console/Network tab/Logs -   NA
Other Environments where this issue exists -  NA
Any similar issue reported in the past -   NA
Any other behaviour to be taken into consideration by Dev before fixing this bug -  NATEST PROOFS:

---

## Comments

_No comments_