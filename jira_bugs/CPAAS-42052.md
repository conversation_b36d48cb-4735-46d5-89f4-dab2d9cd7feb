# CPAAS-42052: Debugger | Few functional issues on the debugger console.

**Status:** To Do  
**Priority:** Minor

---

## Description

Description: EvaluateContextState logs should be removed from the console.Logs are not getting generated after the initial welcome message from the bot.When the user opens the preview page, a default bot message should be displayed to the user.Pre-requisite:
NeuraTalk AI bot builder page implementation is doneSteps to reproduce:Log in to CPaaS  with Enterprise admin credentialsNow, navigate to NeuraTalk AI Click on the CREATE buttonNow, click on the design tab.Create a flow on the canvas.Click on the build icon after creating the flow.Now click on the preview icon.On the preview page, click on the bug icon to open the console panel.Chat with the bot to get the logs in the console.Check the design and text on the console.Expected Result:Only the valid parameters with valid status code should be shown in the console.Actual Result:A few unwanted parameters are displayed in the console.Evidence:-

---

## Comments

_No comments_