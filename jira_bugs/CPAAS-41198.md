# CPAAS-41198: Duplicate FAQ questions can be created across the FAQs, including in different categories.

**Status:** To Do  
**Priority:** High

---

## Description

Description: The system is failing to enforce a uniqueness constraint on FAQ questions, allowing users to create multiple FAQs with the exact same question text. This issue occurs both within a single category and across different categoriesPre-requisite:You must be logged in with a valid Enterprise Admin/User.You must have at least two FAQ categories (e.g., "General" and "Shipping").Steps to Reproduce:Navigate to the ‘Train' → 'FAQs’ tab.Select the "General" category.Add a new FAQ with the question: "What are the hours of operation?" and any answer. Now, add the same question multiple times by clicking on the ‘+ ADD’ button under the questions field.Click on the "ADD" button nd observe the validation messageNow, remove duplicate questions and save the single question.Navigate to the "Shipping" category.Click to add a new FAQ.In the question field, enter the exact same text as in step 3: "What are the hours of operation?".Provide any answer and click "Save".Observe whether the system prevents the creation of duplicate questions.Expected Results:Duplicate questions shouldn’t be allowed across FAQs/CategoriesActual Results:Duplicate questions are allowed across FAQs/CategoriesAttachments:

---

## Comments

_No comments_