# CPAAS-39810: NeuraTalk| 32 character validation is not working correctly on chatbot builder ADD INTENT popup

**Status:** To Do  
**Priority:** Medium

---

## Description

Description: 32 character validation is not working correctly on chatbot builder ADD INTENT popupPre-requisite:
Intent implementation is doneSteps to reproduce:Log in to CPaaS  with Enterprise admin credentialsNow, navigate to NeuraTalk AI Click on Create buttonOn the chatbot builder click Train tabSelect INTENT UTTERANCES sub-tabClick on ADD INTENT buttonCopy 32 characters long text and paste in the Intent Name fieldExpected Result:For 32 characters the ADD button should enabled on the pop-upActual Result:ADD button is disabled for 32 characters. When user type one more character and remove the latest added character, then then ADD button gets enabled

---

## Comments

### Comment by arvind.aswal:
this functionality still has issue