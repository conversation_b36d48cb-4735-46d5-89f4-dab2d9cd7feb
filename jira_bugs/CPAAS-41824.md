# CPAAS-41824: Settings || Language || If the user selects any language and activates it by clicking the save button, a success toaster should be displayed.

**Status:** To Do  
**Priority:** Medium

---

## Description

ISSUE SUMMARY: Settings || Language || If the user selects any language and activates it by clicking the save button, a success toaster should be displayed.Steps To Reproduce:-Log in to the Neuratalk portal  with valid EA/EU credentialsUser is redirected successfully to the Neuratalk homepage.Now, click on the Neuratalk AI module.Now, click on the edit icon on any bot present on the page or click on the create button.Now, click on the Settings tab.Select the language card on the settings page.Select any language.Click on the save button.Expected Results:-A Toaster should be displayed after a successful save.Actual Results:-No toaster is displaying upon saving the selected languages.ADDITIONAL DETAILS:
Browser & Version - Google Chrome Version 106.0.5249.103
Any Error/Warning in Console/Network tab/Logs -   NA
Other Environments where this issue exists -  NA
Any similar issue reported in the past -   NA
Any other behaviour to be taken into consideration by <PERSON> before fixing this bug -  NATEST PROOFS:

---

## Comments

_No comments_