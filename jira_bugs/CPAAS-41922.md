# CPAAS-41922: The language settings are removed if the user selects a language, adds any intents and utterances, and then reloads the page. As a result, the intents, FAQs, and selected languages are also removed from the page.

**Status:** To Do  
**Priority:** Medium

---

## Description

ISSUE SUMMARY: The language settings are removed if the user selects a language, adds any intents and utterances, and then reloads the page. As a result, the intents, FAQs, and selected languages are also removed from the page.Steps To Reproduce:-Log in to the Neuratalk portal  with valid EA/EU credentialsUser is redirected successfully to the Neuratalk homepage.Now, click on the Neuratalk AI module.Now, click on the edit icon on any bot present on the page or click on the create button.Now, click on the settings tab.Click on the language card.Select some languages and click the save button.Now navigate to the FAQ sub tab from -->Train-->FAQ.Add some FAQs with a few translated languages.Navigate to the Intent utterances subtab.Click on the Add Intent button.Add any intent name.Click on the save button.Now, click on the add utterances button.Add any utterances.Now click on the ADD button.Now reload the page.Log out of the application.Log in again and open the same bot.Check the settings, FAQs, and the intent.Expected Results:-Once the user has saved the settings, it should be saved until and unless the user modifies it.Actual Results:-All the saved settings, FAQs, and the intent get lost.ADDITIONAL DETAILS:
Browser & Version - Google Chrome Version 106.0.5249.103
Any Error/Warning in Console/Network tab/Logs -   NA
Other Environments where this issue exists -  NA
Any similar issue reported in the past -   NA
Any other behaviour to be taken into consideration by Dev before fixing this bug -  NATEST PROOFS:

---

## Comments

_No comments_