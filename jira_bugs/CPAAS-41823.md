# CPAAS-41823: Settings || Language || Save button should be disabled by default until and unless the user selects any of the languages. Once the user saves the language, the save button should be disabled again.

**Status:** To Do  
**Priority:** Medium

---

## Description

ISSUE SUMMARY: Settings || Language || Save button should be disabled by default until and unless the user selects any of the languages. Once the user saves the language, the save button should be disabled again.Steps To Reproduce:-Log in to the Neuratalk portal  with valid EA/EU credentialsUser is redirected successfully to the Neuratalk homepage.Now, click on the Neuratalk AI module.Now, click on the edit icon on any bot present on the page or click on the create button.Now, click on the Settings tab.Select the language card on the settings page.Without selecting any language, check the save button.Expected Results:-The save button should be disabled initially. And once the user selects the language, it gets enabled.
Also, once the user selects the language, clicks on the save button, the save button becomes disabled again.Actual Results:-The save button is enabled every time.ADDITIONAL DETAILS:
Browser & Version - Google Chrome Version 106.0.5249.103
Any Error/Warning in Console/Network tab/Logs -   NA
Other Environments where this issue exists -  NA
Any similar issue reported in the past -   NA
Any other behaviour to be taken into consideration by Dev before fixing this bug -  NATEST PROOFS:

---

## Comments

_No comments_