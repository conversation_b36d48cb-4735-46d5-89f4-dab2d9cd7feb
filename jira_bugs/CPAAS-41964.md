# CPAAS-41964: If the user changes the language from the default language, the language tag is not visible on the default language.

**Status:** To Do  
**Priority:** High

---

## Description

ISSUE SUMMARY: If the user changes the language from the default language, the language tag is not visible on the default language.Steps To Reproduce:-Log in to the Neuratalk portal  with valid EA/EU credentialsUser is redirected successfully to the Neuratalk homepage.Now, click on the Neuratalk AI module.Now, click on the edit icon on any bot present on the page or click on the create button.Now, click on the train tab.Navigate to the Intent utterances subtab.Click on the Add Intent button.Add any intent name.Click on the save button.Now, click the add utterance button.Add any utterance.Now click on the ADD button.Now, click on the edit icon from the kebab menu of the utterance.Click on the language dropdown and select any language.Insert the translated languages and click on the save button.Now check the language tag on the utterance.Perform this step multiple times to create multiple translated languages.Check the tag on the default utterance.Expected Results:-If the language is changed for any of the utterances, the language tag should be displayed on each of the translated languages as well as the default language.Actual Results:-The tags are missing on the default language on the utterance.ADDITIONAL DETAILS:
Browser & Version - Google Chrome Version 106.0.5249.103
Any Error/Warning in Console/Network tab/Logs -   NA
Other Environments where this issue exists -  NA
Any similar issue reported in the past -   NA
Any other behaviour to be taken into consideration by Dev before fixing this bug -  NATEST PROOFS:

---

## Comments

_No comments_