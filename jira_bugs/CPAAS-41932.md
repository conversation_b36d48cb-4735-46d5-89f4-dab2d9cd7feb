# CPAAS-41932: If the user creates any questions and utterances with multiple languages and then the user deletes any language, the associated language should also be deleted.

**Status:** To Do  
**Priority:** Medium

---

## Description

ISSUE SUMMARY: If the user creates any questions and utterances with multiple languages, and then the user deletes any language, the associated language should also be deleted.Steps To Reproduce:-Log in to the Neuratalk portal  with valid EA/EU credentialsUser is redirected successfully to the Neuratalk homepage.Now, click on the Neuratalk AI module.Now, click on the edit icon on any bot present on the page or click on the create button.Now, click on the train tab.Navigate to the Intent utterances subtab.Click on the Add Intent button.Add any intent name.Click on the save button.Now, click the add utterances button.Add any utterances.Now click on the ADD button.Now, click on the edit icon from the kebab menu of the utterances.Change the language and insert the utterances with the translated language.Click on the save button.perform this step multiple times to create 3-4 translations.Now, click on the kebab menu and click on the delete icon.Now, change the language from the dropdown and check whether the utterances with the associated language exist or not.Expected Results:-If the user deletes any language, the utterances and questions associated language should also be deleted.Actual Results:-If the user deletes an utterance and a question, the associated language status in the list.ADDITIONAL DETAILS:
Browser & Version - Google Chrome Version 106.0.5249.103
Any Error/Warning in Console/Network tab/Logs -   NA
Other Environments where this issue exists -  NA
Any similar issue reported in the past -   NA
Any other behaviour to be taken into consideration by Dev before fixing this bug -  NATEST PROOFS:

---

## Comments

_No comments_