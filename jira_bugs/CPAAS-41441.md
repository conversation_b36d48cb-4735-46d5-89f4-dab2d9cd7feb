# CPAAS-41441: NeuraTalk| Order of Welcome and Fallback default flow is not correct

**Status:** To Do  
**Priority:** Minor

---

## Description

Description: Order of Welcome and Fallback default flow is not correct. These flow appears automatically with every chat bot on bot creationPre-requisite:
NeuraTalk AI landing page implementation is doneSteps to reproduce:Log in to CPaaS  with Enterprise admin credentialsNow, navigate to NeuraTalk AI Click on CREATE button or any existing bot and observe the Welcome and Fallback flow on bot builder pageExpected Result:The order is always as follows:Welcome FallbackActual Result:The order is sometime as follows:FallbackWelcome

---

## Comments

### Comment by arvind.aswal:
This is not fixed

---
### Comment by arvind.aswal:
it is still not fixed. Try 2-3 times and the issue re-appears