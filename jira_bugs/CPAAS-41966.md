# CPAAS-41966: The left menu of the NeuraTalk portal page keeps flickering after a certain time interval.

**Status:** To Do  
**Priority:** High

---

## Description

ISSUE SUMMARY: The left menu of the NeuraTalk portal page keeps flickering after a certain time interval.Steps To Reproduce:-Log in to the Neuratalk portal  with valid EA/EU credentialsUser is redirected successfully to the Neuratalk homepage.Now, click on the Neuratalk AI module.Now, click on the edit icon on any bot present on the page or click on the create button.Now, click on the Design tab.Now drag and drop some nodes to the canvas.Check the Left pane started blinking.Now navigate to any of the tabs.Check if the blinking is still happening.Expected Results:-The left pane should not flicker.Actual Results:-The modules in the left pane keep flickering every time after 2-3 seconds.ADDITIONAL DETAILS:
Browser & Version - Google Chrome Version 106.0.5249.103
Any Error/Warning in Console/Network tab/Logs -   NA
Other Environments where this issue exists -  NA
Any similar issue reported in the past -   NA
Any other behaviour to be taken into consideration by <PERSON> before fixing this bug -  NATEST PROOFS:

---

## Comments

_No comments_