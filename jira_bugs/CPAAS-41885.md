# CPAAS-41885: No separate input field is displayed for the translated language while adding the FAQs/utterances.

**Status:** To Do  
**Priority:** Medium

---

## Description

ISSUE SUMMARY: No separate input field is displayed for the translated language while adding the FAQs/utterances.Steps To Reproduce:-Log in to the Neuratalk portal  with valid EA/EU credentialsUser is redirected successfully to the Neuratalk homepage.Now, click on the Neuratalk AI module.Now, click on the edit icon on any bot present on the page or click on the create button.Now, click on the Train tab or the Intent tab.Check the default language in the dropdown.Now add some questions or utterances.Now again click on the language dropdown and select any desired language.Now check the separate input field for the translated language.Expected Results:-As per the story, if the user changes the language, a separate input field should be displayed to add translated questions/answers or utterances.Actual Results:-No separate input field is displayed after changing the language from the dropdown.ADDITIONAL DETAILS:
Browser & Version - Google Chrome Version 106.0.5249.103
Any Error/Warning in Console/Network tab/Logs -   NA
Other Environments where this issue exists -  NA
Any similar issue reported in the past -   NA
Any other behaviour to be taken into consideration by <PERSON> before fixing this bug -  NATEST PROOFS:

---

## Comments

_No comments_