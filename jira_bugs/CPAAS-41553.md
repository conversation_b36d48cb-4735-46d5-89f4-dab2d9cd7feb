# CPAAS-41553: If the user searches for any node or flow and clicks on cross icon to clear the search field, the user is not able to view the other nodes/flows

**Status:** To Do  
**Priority:** Medium

---

## Description

Description: If the user searches for any node/Flows and clears the search field, the user is not able to view the other nodes/Flows, even if the searched result is cleared.Same issue while searching for the non-existing node, the node list gets blanked, even if the user clears the search field.Pre-requisite:
NeuraTalk AI bot builder page implementation is doneSteps to reproduce:Log in to CPaaS  with Enterprise admin credentialsNow, navigate to NeuraTalk AI Click on the CREATE buttonOn the node palette, click on the Search iconEnter any node name.Check the searched results.Now click on the cross icon.Check that the pre-existing nodes are visible in the list or not.Expected Result:If the user clears the input from the search field, the user can view the default list of the nodes.Actual Result:The Node palette is showing the searched result even if the user clears the search field.Evidences:-

---

## Comments

_No comments_