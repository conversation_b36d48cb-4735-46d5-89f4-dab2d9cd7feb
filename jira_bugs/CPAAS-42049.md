# CPAAS-42049: NeuraTalk| User is not able to remove link flow from FAQ

**Status:** To Do  
**Priority:** High

---

## Description

Description: User is not able to remove link flow from FAQPre-requisite:
User is logged in to neuratalk portal with enterprise user or enterprise admin accountThere should exist a bot in the system which already has some flows addedAn FAQ is already added in the bot with a link flow attachedSteps to reproduce:Login as Enterprise Admin Click on the to Neuratalk module.select any exisitng bot.click on the �Train� tabOpen FAQ modal by clicking in the FAQ sub tab. Observe available fieldsLocate the FAQ entryClick on Overflow menu and edit the FAQTry to remove the selected link flowObservation:User is not able to remove the link flow from FAQ

---

## Comments

_No comments_