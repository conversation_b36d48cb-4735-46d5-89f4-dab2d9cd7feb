# CPAAS-41826: If the user selects more than 20 languages from the settings, only 20 languages are visible in the dropdown list of FAQ's and the Utterances tab.

**Status:** To Do  
**Priority:** Medium

---

## Description

ISSUE SUMMARY: If the user selects more than 20 languages from the settings, only 20 languages are visible in the dropdown list of FAQ's and the Utterances tab.Steps To Reproduce:-Log in to the Neuratalk portal  with valid EA/EU credentialsUser is redirected successfully to the Neuratalk homepage.Now, click on the Neuratalk AI module.Now, click on the edit icon on any bot present on the page or click on the create button.Now, click on the Settings tab.Select the language card on the settings page.Select a total of 30 languages.Click on the save button.Now navigate to the FAQs tab, click on the language dropdown.Check whether all the selected options are visible in the dropdown.Now navigate to the intent tab, click on the language dropdown.Check whether all the selected options are visible in the dropdown.Expected Results:-If the user selects all the languages from the language section, those selected languages should be visible across all the dropdown fields on the bot.Actual Results:-Only 20 languages are displayed in the language dropdown.ADDITIONAL DETAILS:
Browser & Version - Google Chrome Version 106.0.5249.103
Any Error/Warning in Console/Network tab/Logs -   NA
Other Environments where this issue exists -  NA
Any similar issue reported in the past -   NA
Any other behaviour to be taken into consideration by Dev before fixing this bug -  NATEST PROOFS:

---

## Comments

_No comments_