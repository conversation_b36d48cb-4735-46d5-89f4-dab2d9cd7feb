# CPAAS-41151: Mutiple Issues in ADD QUESTION modal functionality

**Status:** To Do  
**Priority:** Major

---

## Description

Pre-requisite:You must be logged in with a valid Enterprise Admin/User.You must be on the ‘Train' → 'FAQs’ tab, with the "Add Question" or "Edit Question" modal open.Detailed Breakdown of IssuesBelow are the specific issues, grouped by functionality, with steps to reproduce each.1. Data Handling & Whitespace InconsistenciesIssue A: Inconsistent Trimming: The Question field correctly trims leading/trailing spaces, but the Answer field does not, leading to inconsistent data being saved.Issue B: UI Breaking on Paste: Pasting only whitespace into a field breaks the input field's UI.Steps to Reproduce:In the Question field, enter " Test Question ".In the Answer field, enter " Test Answer ". Save and then re-edit the FAQ.Create a new FAQCopy a string of only spaces (e.g., " ") and paste it into the now-empty Answer field.Expected Results:Both fields should have whitespace trimmed upon saving. Editing should show "Test Question" and "Test Answer".The UI should remain stable when spaces are pasted.Actual Results:Upon re-editing, the Question is trimmed, but the Answer field still contains " Test Answer ".Pasting only spaces into the field breaks its visual layout.Attachment:2. Hyperlink Functionality FailuresIssue A: Missing URL Validation: The modal does not display an error for invalid URL formats.Issue B: Missing Max Length Validation: No error is shown if the Display Text exceeds its 100-character limit.Issue C: Edit State Not Populated: When editing a hyperlink, the "Display Text" field is not pre-populated.Issue D (Platform Inconsistency): The modal's design/behavior is inconsistent with other parts of the platform.Steps to Reproduce:Highlight text in the Answer field and click the "Add Hyperlink" icon.Test Invalid URL: In the URL field, enter "not-a-real-url" and observe.Test Max Length: Clear the fields. In the Display Text field, enter a string longer than 100 characters. Observe for any error messages.Now, create a valid hyperlink and save the FAQ.Re-edit the FAQ, select the same hyperlinked text, and click the "Add Hyperlink" icon again.Expected Results:An inline error should appear for the invalid URL.When the Display Text exceeds 100 characters, an error message should appear (e.g., "Display text cannot exceed 100 characters"), and the save button should be disabled.When editing a hyperlink, the modal should be pre-populated with the existing data.Design should be consistent across platforms, Neuratalk/Agent PortalActual Results:No error message is displayed for the invalid URL.No error message is displayed when the Display Text is over 100 characters.When editing the hyperlink, the "Display Text" field is empty.Design is not consistent across platforms, Neuratalk/Agent PortalAttachments:
3. Rich Text Editor Rendering & Layout BugsIssue A: Formatting Not Displayed: Text formatting (bold, italics, hyperlinks) created in the editor is not rendered in the final FAQ list view.Issue B: UI Break on Long Text: A long, unbroken string of text in the Answer field breaks the text editor's layout.Issue C: Emoji Picker Modal Position: The emoji picker modal appears within the boundaries of the answer field instead of overlapping it.Steps to Reproduce:In the Answer field, type text and apply any text editor formatting(Say, bold formatting) and add a hyperlink. Save the FAQ.View the FAQ you just created in the main list.Edit an FAQ and paste a very long string with no spaces into the Answer field.Observe the position of the emoji icon and observe.Expected Results:The saved FAQ should display the applied formatting (bold) and hyperlinked text correctly.The text editor should handle long strings gracefully (e.g., with text wrapping or a scrollbar).The emoji picker modal should slightly overlap the field's border for better UI.Actual Results:The final FAQ displays as plain text; all formatting is lost.The long string overflows its container and breaks the editor's UI.The emoji icon is displayed inside the field's border.Attachment:
4. Emoji Functionality FailuresIssue A: Broken Insertion Point: Inserting an emoji places it at the end of the text, regardless of the cursor's actual position.Issue B: Broken Emoji Modal UI: The emoji picker modal itself has a broken or distorted layout.Steps to Reproduce:In the Answer field, type "First half, second half".Place your cursor after the comma: "First half, | second half".Click the emoji icon to open the picker. Observe its layout.Select any emoji.Expected Results:The emoji modal should display correctly.The emoji should be inserted at the cursor's position: "First half, 😊 second half".Should be consistent around Neuratalk/Agent PortalActual Results:The emoji picker's UI is broken/unusable. (The search field, icons, padding)The emoji is inserted at the very end of the line: "First half, second half 😊".Not consistent across Neuratalk/Agent Portal5. Required Field Validation FailuresIssue A: No Validation on Empty Question Field: The "required field" validation for the Question field does not trigger correctly after text is entered and then removed.Issue B: Incorrect Validation on Empty Answer Field: The Answer field displays an incorrect or inappropriate validation message after text is entered and then removed.Steps to Reproduce:Open the "Add FAQ" modal. Note that the "Save" button is disabled.Test Question Field: Type any text into the Question field (e.g., "Test"). The "Save" button is enabled.Now, delete all the text from the Question field.Observe the UI for any error messages and check the state of the "Save" button.Test Answer Field: Type any text into the Answer field.Now, delete all the text from the Answer field.Observe the validation message that is displayed.Expected Results:After clearing either field, the "Save" button should become disabled.A clear and specific error message should appear for each empty field (e.g., "Question is required" and "Answer is required").Actual Results:For the Question field: After clearing the text, no error message is displayed, and the "Save" button remains enabled.For the Answer field: After clearing the text, an incorrect validation message “faqs.validation.answerRequired“ is displayed.

---

## Comments

_No comments_