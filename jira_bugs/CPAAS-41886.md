# CPAAS-41886: If the user selects the translated language and inserts the questions, no "Save" button is displayed to save the translated language.

**Status:** To Do  
**Priority:** Medium

---

## Description

ISSUE SUMMARY: If the user selects the translated language and inserts the questions, no Save button is displayed to save the translated language.Steps To Reproduce:-Log in to the Neuratalk portal  with valid EA/EU credentialsUser is redirected successfully to the Neuratalk homepage.Now, click on the Neuratalk AI module.Now, click on the edit icon on any bot present on the page or click on the create button.Now, click on the Train tab.Navigate to the FAQ subtab.Click on the Add question button.Add any question.Now, check the save button to save the translated language.Expected Results:-As per the story, there should be a Save button to save the translated questions.Actual Results:-The user can see the “ADD” button instead of “Save” to save the language.ADDITIONAL DETAILS:
Browser & Version - Google Chrome Version 106.0.5249.103
Any Error/Warning in Console/Network tab/Logs -   NA
Other Environments where this issue exists -  NA
Any similar issue reported in the past -   NA
Any other behaviour to be taken into consideration by <PERSON> before fixing this bug -  NATEST PROOFS:

---

## Comments

_No comments_