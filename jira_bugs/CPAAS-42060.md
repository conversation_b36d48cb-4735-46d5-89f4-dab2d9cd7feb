# CPAAS-42060: Build | User is getting a 500 error once they create any flow and clicks on the build button.

**Status:** To Do  
**Priority:** Blocker

---

## Description

Description: Build | User is getting a 500 error once they create any flow and click on the build button.Pre-requisite:
NeuraTalk AI bot builder page implementation is doneSteps to reproduce:Log in to CPaaS  with Enterprise admin credentialsNow, navigate to NeuraTalk AI Click on the CREATE buttonNow, click on the design tab.Create a flow on the canvas.Click on the build icon after creating the flow.Expected Result:The bot flow should get created after the build button is triggered.Actual Result:The user gets a 500 error after clicking on the build button.Evidence:-

---

## Comments

_No comments_