# CPAAS-42070: [FAQ]: Adding more than 10 FAQs in a catgeory results in loss of previous FAQs

**Status:** To Do  
**Priority:** Major

---

## Description

Description: Unable to add category with a valid namePre-requisite:Valid Enterprise Admin/User should existThere should exist a category with 10 FAQsSteps to Reproduce:Log in to the CPAAS application.Navigate to the 'Neuratalk AI 'pageClick on any existing bot or click on the ‘CREATE’ buttonNavigate to ‘Train' → 'FAQs’ tabClick on the category having 10 FAQsObserve the old FAQ at the bottomNow, add a new FAQ via ADD QUESTIONObserve the old FAQ at the bottomExpected Results: The category should have no limit for FAQs, and all the FAQs should be displayed correctlyActual Results: Adding more than 10 FAQs in a category results in the loss of previous FAQsAttachments:

---

## Comments

_No comments_