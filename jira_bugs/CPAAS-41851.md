# CPAAS-41851: If the user selects more than 20 languages from the settings, the default language changes in the dropdown of the FAQs and Utterances tab.

**Status:** To Do  
**Priority:** Medium

---

## Description

ISSUE SUMMARY: If the user selects more than 20 languages from the settings, the default language changes in the dropdown of the FAQs and Utterances tab.Steps To Reproduce:-Log in to the Neuratalk portal  with valid EA/EU credentialsUser is redirected successfully to the Neuratalk homepage.Now, click on the Neuratalk AI module.Now, click on the edit icon on any bot present on the page or click on the create button.Now, click on the Settings tab.Select the language card on the settings page.Select a total of 30 languages.Click on the save button.Now navigate to the FAQs tab.Check the default language in the language dropdown.Now navigate to the intent tab.Check the default language in the language dropdown.Expected Results:-The default language should be the only language that is the default in the settings.Actual Results:-Some random language is visible as the default language on both the tabs.ADDITIONAL DETAILS:
Browser & Version - Google Chrome Version 106.0.5249.103
Any Error/Warning in Console/Network tab/Logs -   NA
Other Environments where this issue exists -  NA
Any similar issue reported in the past -   NA
Any other behaviour to be taken into consideration by Dev before fixing this bug -  NATEST PROOFS:

---

## Comments

_No comments_