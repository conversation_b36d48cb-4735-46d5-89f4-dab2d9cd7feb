# CPAAS-41940: While editing the utterances, if the user changes the language from the dropdown, the field shows the previously added utterances.

**Status:** To Do  
**Priority:** Medium

---

## Description

ISSUE SUMMARY: While editing the utterances, if the user changes the language from the dropdown, the field shows the previously added utterances.Steps To Reproduce:-Log in to the Neuratalk portal  with valid EA/EU credentialsUser is redirected successfully to the Neuratalk homepage.Now, click on the Neuratalk AI module.Now, click on the edit icon on any bot present on the page or click on the create button.Now, click on the train tab.Navigate to the Intent utterances subtab.Click on the Add Intent button.Add any intent name.Click on the save button.Now, click the add utterances button.Add any utterances.Now click on the ADD button.Now, click on the edit icon from the kebab menu of the utterances.Click on the language dropdown and select any language.Check whether the utterances field becomes blank or not.Expected Results:-If the user changes the language, the field should get reset to empty, or if any language is already configured, the field should display the configured language.Actual Results:-By changing the language, the field shows the utterances of the default language.ADDITIONAL DETAILS:
Browser & Version - Google Chrome Version 106.0.5249.103
Any Error/Warning in Console/Network tab/Logs -   NA
Other Environments where this issue exists -  NA
Any similar issue reported in the past -   NA
Any other behaviour to be taken into consideration by Dev before fixing this bug -  NATEST PROOFS:

---

## Comments

_No comments_