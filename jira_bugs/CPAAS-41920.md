# CPAAS-41920: The "ADD" button is not responding if the user clicks on the "ADD" button after adding the utterances.

**Status:** To Do  
**Priority:** Medium

---

## Description

ISSUE SUMMARY: The "ADD" button is not responding if the user clicks on the ADD" button after adding the utterances.Steps To Reproduce:-Log in to the Neuratalk portal  with valid EA/EU credentialsUser is redirected successfully to the Neuratalk homepage.Now, click on the Neuratalk AI module.Now, click on the edit icon on any bot present on the page or click on the create button.Now, click on the Train tab.Navigate to the Intent utterances subtab.Click on the Add Intent button.Add any intent name.Click on the save button.Now, click on the add utterances button.Add multiple utterances.Now click on the ADD button.Expected Results:-The “ADD” button should save the utterances.Actual Results:-The “ADD” button is not responding.ADDITIONAL DETAILS:
Browser & Version - Google Chrome Version 106.0.5249.103
Any Error/Warning in Console/Network tab/Logs -   NA
Other Environments where this issue exists -  NA
Any similar issue reported in the past -   NA
Any other behaviour to be taken into consideration by Dev before fixing this bug -  NATEST PROOFS:

---

## Comments

_No comments_