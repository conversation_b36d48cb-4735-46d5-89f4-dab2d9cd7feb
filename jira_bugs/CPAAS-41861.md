# CPAAS-41861: The "ADD" CTA button opens up another row of questions, and the user is unable to save the questions and answer if the user tries to create a question and answer in a language other than English.

**Status:** To Do  
**Priority:** Medium

---

## Description

ISSUE SUMMARY: The "ADD" CTA button opens up another row of questions, and the user is unable to save the questions and answer if the user tries to create a question and answer in a language other than English.Steps To Reproduce:-Log in to the Neuratalk portal  with valid EA/EU credentialsUser is redirected successfully to the Neuratalk homepage.Now, click on the Neuratalk AI module.Now, click on the edit icon on any bot present on the page or click on the create button.Now, click on the Train tab.Navigate to the FAQ subtab.Click on the Add question button.Add any question.Now, click on the +Add button to add multiple alternate questions.Now, click on the “ADD” CTA button.Expected Results:-The questions should be saved, and the pop-up should be closed if the user clicks on the “ADD” CTA button.Actual Results:-If the user clicks on the “ADD” CTA button, the question field adds another question row.ADDITIONAL DETAILS:
Browser & Version - Google Chrome Version 106.0.5249.103
Any Error/Warning in Console/Network tab/Logs -   NA
Other Environments where this issue exists -  NA
Any similar issue reported in the past -   NA
Any other behaviour to be taken into consideration by Dev before fixing this bug -  NATEST PROOFS:

---

## Comments

_No comments_