# CPAAS-41968: The whole design page sticks and becomes unresponsive once the user opens the left window of "Form node, clicks on the dropdown, and then clicks outside of the canvas without closing the left window.

**Status:** To Do  
**Priority:** Blocker

---

## Description

ISSUE SUMMARY: The whole design page sticks and becomes unresponsive once the user opens the left window of "Form node, clicks on the dropdown, and then clicks outside of the canvas without closing the left window.Steps To Reproduce:-Log in to the Neuratalk portal  with valid EA/EU credentialsUser is redirected successfully to the Neuratalk homepage.Now, click on the Neuratalk AI module.Now, click on the edit icon on any bot present on the page or click on the create button.Now, click on the Design tab.Now drag and drop “Form” nodes to the canvas.Click on the node.Now, click on the dropdown on the left window.Without closing the left window, click outside the canvas.Now, try to click any of the buttons visible on the page.Expected Results:-If the user clicks on the outer canvas, the left window should be closed, and the other buttons should be clickable.Actual Results:-The page becomes unresponsive, which blocks the user from performing any action.ADDITIONAL DETAILS:
Browser & Version - Google Chrome Version 106.0.5249.103
Any Error/Warning in Console/Network tab/Logs -   NA
Other Environments where this issue exists -  NA
Any similar issue reported in the past -   NA
Any other behaviour to be taken into consideration by Dev before fixing this bug -  NATEST PROOFS:

---

## Comments

_No comments_