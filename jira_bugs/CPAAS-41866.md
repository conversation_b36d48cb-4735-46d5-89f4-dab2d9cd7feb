# CPAAS-41866: The user can add more than 10 alternative questions for any question.

**Status:** To Do  
**Priority:** Medium

---

## Description

ISSUE SUMMARY: The user can add more than 10 alternative questions per question.Steps To Reproduce:-Log in to the Neuratalk portal  with valid EA/EU credentialsUser is redirected successfully to the Neuratalk homepage.Now, click on the Neuratalk AI module.Now, click on the edit icon on any bot present on the page or click on the create button.Now, click on the Train tab.Navigate to the FAQ subtab.Click on the Add question button.Add any question.Now, click on the +Add button to add multiple alternate questions.Try to add more than 10 alternative questions.Expected Results:-The system should restrict the user from adding more than 10 alt questions, or the “+add” should get disabled after adding 10 questions.Actual Results:-The user can add more than 10 alternative questions.ADDITIONAL DETAILS:
Browser & Version - Google Chrome Version 106.0.5249.103
Any Error/Warning in Console/Network tab/Logs -   NA
Other Environments where this issue exists -  NA
Any similar issue reported in the past -   NA
Any other behaviour to be taken into consideration by <PERSON> before fixing this bug -  NATEST PROOFS:

---

## Comments

_No comments_